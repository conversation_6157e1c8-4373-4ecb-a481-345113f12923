codecov:
  token: 41307ad9-2594-4211-acb5-cfb401572fd5

coverage:
  precision: 2
  round: down
  range: "70...100"
  status:
    project:
      default:
        target: 80%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error
    patch:
      default:
        target: 80%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

comment:
  layout: "reach,diff,flags,tree,reach"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes
  branches:
    - main
    - develop

ignore:
  - "bootstrap/**/*"
  - "config/**/*"
  - "database/migrations/**/*"
  - "database/seeders/**/*"
  - "public/**/*"
  - "resources/**/*"
  - "routes/**/*"
  - "storage/**/*"
  - "vendor/**/*"
  - "tests/**/*"