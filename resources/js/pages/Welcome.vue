<script setup lang="ts">
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Icon from '@/components/Icon.vue';
</script>

<template>
    <Head title="Welcome to re:do">
        <meta name="description" content="re:do - A smart todo list application with daily refresh functionality, hierarchical organization, and timezone-aware task management." />
    </Head>

    <div class="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <!-- Navigation Header -->
        <header class="relative z-10 w-full border-b border-border/40 bg-background/80 backdrop-blur-sm">
            <div class="mx-auto flex h-16 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
                <div class="flex items-center gap-2">
                    <AppLogoIcon class="h-8 w-8 fill-current text-foreground" />
                    <span class="text-xl font-semibold text-foreground">re:do</span>
                </div>

                <nav class="flex items-center gap-4">
                    <Link
                        v-if="$page.props.auth.user"
                        :href="route('dashboard')"
                        class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                    >
                        Go to Dashboard
                    </Link>
                    <template v-else>
                        <Link
                            :href="route('login')"
                            class="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-foreground transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        >
                            Log in
                        </Link>
                        <Link
                            :href="route('register')"
                            class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        >
                            Get Started
                        </Link>
                    </template>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <main class="relative">
            <div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 sm:py-24 lg:px-8 lg:py-32">
                <div class="text-center">
                    <div class="mb-8 flex justify-center">
                        <Badge variant="secondary" class="px-3 py-1 text-sm">
                            <Icon name="sparkles" class="mr-2 h-4 w-4" />
                            Smart Todo Management
                        </Badge>
                    </div>

                    <h1 class="mx-auto max-w-4xl text-4xl font-bold tracking-tight text-foreground sm:text-5xl lg:text-6xl">
                        Organize your tasks with
                        <span class="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                            re:do
                        </span>
                    </h1>

                    <p class="mx-auto mt-6 max-w-2xl text-lg leading-8 text-muted-foreground">
                        A smart todo list application with daily refresh functionality, hierarchical organization,
                        and timezone-aware task management. Stay organized and productive with intelligent task recreation.
                    </p>

                    <div class="mt-10 flex items-center justify-center gap-4">
                        <Link
                            v-if="!$page.props.auth.user"
                            :href="route('register')"
                            class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-base font-medium text-primary-foreground shadow-sm transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        >
                            <Icon name="arrow-right" class="mr-2 h-4 w-4" />
                            Get Started Free
                        </Link>
                        <Link
                            v-else
                            :href="route('dashboard')"
                            class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-base font-medium text-primary-foreground shadow-sm transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        >
                            <Icon name="arrow-right" class="mr-2 h-4 w-4" />
                            Go to Dashboard
                        </Link>
                        <Link
                            v-if="!$page.props.auth.user"
                            :href="route('login')"
                            class="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-3 text-base font-medium text-foreground shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        >
                            Sign In
                        </Link>
                    </div>
                </div>
            </div>
        </main>

        <!-- Features Section -->
        <section class="relative py-16 sm:py-24">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                        Everything you need to stay organized
                    </h2>
                    <p class="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
                        re:do combines powerful features with intelligent automation to help you manage your tasks effortlessly.
                    </p>
                </div>

                <div class="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                    <!-- Hierarchical Organization -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="folder-tree" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Hierarchical Lists</CardTitle>
                            <CardDescription>
                                Organize your todos in nested lists with clear ownership and inheritance patterns.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <!-- Daily Refresh -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="refresh-cw" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Daily Refresh</CardTitle>
                            <CardDescription>
                                Automatically recreate your daily tasks based on your timezone settings for consistent routines.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <!-- Date Filtering -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="calendar" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Smart Filtering</CardTitle>
                            <CardDescription>
                                Filter tasks by date with intuitive calendar components and quick shortcuts like 'Today' and 'Yesterday'.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <!-- Completion Tracking -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="check-circle" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Completion Tracking</CardTitle>
                            <CardDescription>
                                Track completion with timestamps, not just checkboxes. See exactly when tasks were completed.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <!-- Timezone Aware -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="globe" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Timezone Aware</CardTitle>
                            <CardDescription>
                                Intelligent task management that respects your timezone for accurate daily task recreation.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <!-- Progress Insights -->
                    <Card class="relative overflow-hidden">
                        <CardHeader>
                            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                <Icon name="bar-chart-3" class="h-6 w-6 text-primary" />
                            </div>
                            <CardTitle class="text-xl">Progress Insights</CardTitle>
                            <CardDescription>
                                View completion percentages and progress metrics calculated based on your filtered tasks.
                            </CardDescription>
                        </CardHeader>
                    </Card>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="relative py-16 sm:py-24">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="relative overflow-hidden rounded-2xl bg-primary px-6 py-16 text-center shadow-2xl sm:px-16">
                    <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary/80" />
                    <div class="relative">
                        <h2 class="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
                            Ready to get organized?
                        </h2>
                        <p class="mx-auto mt-4 max-w-2xl text-lg text-primary-foreground/90">
                            Join thousands of users who have transformed their productivity with re:do's intelligent task management.
                        </p>
                        <div class="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row">
                            <Link
                                v-if="!$page.props.auth.user"
                                :href="route('register')"
                                class="inline-flex items-center justify-center rounded-md bg-background px-6 py-3 text-base font-medium text-foreground shadow-sm transition-colors hover:bg-background/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                            >
                                <Icon name="arrow-right" class="mr-2 h-4 w-4" />
                                Start Free Today
                            </Link>
                            <Link
                                v-else
                                :href="route('dashboard')"
                                class="inline-flex items-center justify-center rounded-md bg-background px-6 py-3 text-base font-medium text-foreground shadow-sm transition-colors hover:bg-background/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                            >
                                <Icon name="arrow-right" class="mr-2 h-4 w-4" />
                                Go to Dashboard
                            </Link>
                            <span class="text-sm text-primary-foreground/80">
                                No credit card required
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="border-t border-border/40 bg-background/50 backdrop-blur-sm">
            <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                <div class="flex flex-col items-center justify-between gap-4 sm:flex-row">
                    <div class="flex items-center gap-2">
                        <AppLogoIcon class="h-6 w-6 fill-current text-foreground" />
                        <span class="text-lg font-semibold text-foreground">re:do</span>
                    </div>
                    <p class="text-sm text-muted-foreground">
                        © {{ new Date().getFullYear() }} re:do. Built with care for productivity.
                    </p>
                </div>
            </div>
        </footer>
    </div>
</template>